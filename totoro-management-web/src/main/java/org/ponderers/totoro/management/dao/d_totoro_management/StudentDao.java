package org.ponderers.totoro.management.dao.d_totoro_management;

import io.mybatis.mapper.Mapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.ponderers.boot.mybatis.annotation.MyBatisRepository;
import org.ponderers.commons.util.model.pagination.Pagination;
import org.ponderers.totoro.management.dao.d_totoro_management.po.StudentPo;

import java.util.List;

@MyBatisRepository
public interface StudentDao extends Mapper<StudentPo, Long> {
    
    @Select("select * from student where gid = #{gid}")
    StudentPo getStudentById(@Param("gid") long gid);
    
    @Select("<script>select * from student where 1 = 1 <if test=\"nickname != null and nickname != ''\">and nickname = #{nickname}</if></script>")
    StudentPo getStudentByNickname(@Param("nickname") String nickname);
    
    @Select("select * from student limit #{pagination.offset},#{pagination.limit}")
    List<StudentPo> getStudentList(@Param("pagination") Pagination pagination);
    
    @Insert("INSERT INTO student (gid, nickname, age, id_card, create_time, update_time) " +
            "VALUES (#{student.gid}, #{student.nickname}, #{student.age}, #{student.idCard}, #{student.createTime}, #{student.updateTime})")
    int insert(@Param("student") StudentPo student);
    
    @Update("update student set nickname=#{student.nickname}, age=#{student.age}, id_card=#{student.idCard}, update_time=#{student.updateTime} where gid=#{gid}")
    int updateStudent(@Param("gid") long gid, @Param("student") StudentPo student);
    
    @Select("select count(*) from student")
    int getStudentCount();
}
