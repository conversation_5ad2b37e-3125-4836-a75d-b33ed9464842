package org.ponderers.totoro.management.dao.d_totoro_management.po;

import io.mybatis.provider.Entity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 学生
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity.Table("student")
public class StudentPo {
    @Entity.Column(id = true)
    private Long gid;
    @Entity.Column
    private String nickname;
    @Entity.Column
    private Integer age;
    @Entity.Column
    private String idCard;
    private Date createTime;
    private Date updateTime;
}
